<!DOCTYPE html>

<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>" class="light-style layout-navbar-fixed layout-wide" dir="ltr" data-theme="theme-default" data-assets-path="<?php echo e(asset('assets/')); ?>" data-template="front-pages">

<head>
    
    <?php echo $__env->make('layouts.website.partials.metas', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    

    <title><?php echo e(config('app.name')); ?> || <?php echo $__env->yieldContent('page_title'); ?></title>
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo e(asset(config('app.favicon'))); ?>" />

    <!-- Start css -->
    <?php echo $__env->make('layouts.website.partials.stylesheet', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <!-- End css -->
</head>

<body>
    
    <?php echo $__env->make('layouts.website.partials.navbar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    

    <!-- Sections:Start -->

    <div data-bs-spy="scroll" class="scrollspy-example">
        <?php if($errors->any()): ?>
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="alert alert-danger alert-dismissible" role="alert">
                            <i class="ti ti-ban mr-3" style="margin-top: -3px;"></i>
                            <?php echo e($error); ?>

                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        <?php endif; ?>

        <?php if(session('error')): ?>
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="alert alert-danger alert-dismissible" role="alert">
                        <i class="ti ti-ban mr-3" style="margin-top: -3px;"></i>
                        <?php echo e(session('error')); ?>

                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Start row -->
        <?php echo $__env->yieldContent('content'); ?>
        <!-- End row -->
    </div>

    <!-- / Sections:End -->

    
    <?php echo $__env->make('layouts.website.partials.footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    

    <!-- Start js -->
    <?php echo $__env->make('layouts.website.partials.scripts', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <!-- End js -->

    
    <?php echo $__env->make('sweetalert::alert', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
</body>

</html>
<?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrangeMultiTenant\resources\views/layouts/website/app.blade.php ENDPATH**/ ?>