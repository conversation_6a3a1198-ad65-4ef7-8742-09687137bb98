<!-- Core JS -->
<!-- build:js assets/vendor/js/core.js -->

<script src="<?php echo e(asset('assets/vendor/libs/jquery/jquery.js')); ?>"></script>
<script src="<?php echo e(asset('assets/vendor/libs/popper/popper.js')); ?>"></script>
<script src="<?php echo e(asset('assets/vendor/js/bootstrap.js')); ?>"></script>
<script src="<?php echo e(asset('assets/vendor/libs/node-waves/node-waves.js')); ?>"></script>
<script src="<?php echo e(asset('assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.js')); ?>"></script>
<script src="<?php echo e(asset('assets/vendor/libs/hammer/hammer.js')); ?>"></script>

<script src="<?php echo e(asset('assets/js/custom_js/jquery-confirm/jquery-confirm.min.js')); ?>"></script>
<script src="<?php echo e(asset('assets/js/custom_js/jquery-confirm/confirm.js')); ?>"></script>

<script src="<?php echo e(asset('assets/vendor/js/menu.js')); ?>"></script>

<!-- endbuild -->

<!-- Vendors JS -->


<script>
    var unreadNotificationsUrl = "<?php echo e(url('/notification/get-unread-notifications-for-browser')); ?>";
    var markNotificationReadUrl = "<?php echo e(url('/notification/mark-as-read-notifications-for-browser/')); ?>";
</script>
<script src="<?php echo e(asset('assets/js/custom_js/notification/browser_notification.js')); ?>"></script>

<!-- Main JS -->
<script src="<?php echo e(asset('assets/js/main.js')); ?>"></script>


<script src="<?php echo e(asset('assets/js/custom.js')); ?>"></script>

<!-- Page JS -->
<?php echo $__env->yieldContent('script_links'); ?>

<?php echo $__env->yieldContent('custom_script'); ?>
<?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrangeMultiTenant\resources\views/layouts/website/partials/scripts.blade.php ENDPATH**/ ?>