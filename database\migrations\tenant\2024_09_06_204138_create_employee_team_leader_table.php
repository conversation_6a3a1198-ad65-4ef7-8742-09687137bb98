<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('employee_team_leader', function (Blueprint $table) {
            $table->id();

            $table->foreignId('employee_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('team_leader_id')->constrained('users')->onDelete('cascade');
            $table->boolean('is_active')->default(true);

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('employee_team_leader');
    }
};
